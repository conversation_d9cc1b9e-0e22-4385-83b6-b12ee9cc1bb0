//
//  ParkingListBottomSheet.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI

struct ParkingListBottomSheet: View {
    @Binding var searchText: String
    let parkingLocations: [ParkingLocation]
    @ObservedObject var speechRecognizer: SpeechRecognizer
    @Binding var selectedParkingFromMap: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Search bar (always visible)
            HStack(spacing: 12) {
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16))

                    TextField("Search", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.system(size: 16))

                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color(.systemGray6))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )

                // Voice search button
                Button(action: {
                    if speechRecognizer.isRecording {
                        speechRecognizer.stopRecording()
                    } else {
                        speechRecognizer.startRecording()
                    }
                }) {
                    Image(systemName: "mic.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .frame(width: 50, height: 50)
                        .background(Circle().fill(speechRecognizer.isRecording ? .red : .blue))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)

            // Scrollable content, visible when expanded
            ScrollView {
                ParkingListView(
                    parkingLocations: parkingLocations,
                    searchText: searchText,
                    selectedParkingFromMap: $selectedParkingFromMap,
                    onParkingLocationSelected: onParkingLocationSelected
                )
                .padding(.top, 4) // Adjust spacing between search and content
            }
        }
        .background(
            // 使用半透明背景，保持地图可见性
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
            .fill(.ultraThinMaterial)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .ignoresSafeArea(.all, edges: .bottom) // 延伸到屏幕底部
        )
    }
}

// MARK: - Preview
struct ParkingListBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        ParkingListBottomSheet(
            searchText: .constant(""),
            parkingLocations: [
                ParkingLocation(
                    id: 1,
                    name: "Central Parking",
                    address: "123 Collins Street, Melbourne VIC 3000",
                    price: "$15",
                    spots: "25",
                    latitude: -37.8136,
                    longitude: 144.9631
                ),
                ParkingLocation(
                    id: 2,
                    name: "City Square Parking",
                    address: "456 Bourke Street, Melbourne VIC 3000",
                    price: "$12",
                    spots: "18",
                    latitude: -37.8140,
                    longitude: 144.9633
                )
            ],
            speechRecognizer: SpeechRecognizer(),
            selectedParkingFromMap: .constant(nil),
            onParkingLocationSelected: { _ in }
        )
    }
}
