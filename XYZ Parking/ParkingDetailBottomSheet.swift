//
//  ParkingDetailBottomSheet.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI

struct ParkingDetailBottomSheet: View {
    let parkingLocation: ParkingLocation
    let onBack: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Top bar with back button
            HStack {
                Button(action: onBack) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back to List")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.blue)
                }
                
                Spacer()
                
                Text("Parking Details")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // Invisible button for balance
                Button(action: {}) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back to List")
                            .font(.system(size: 16, weight: .medium))
                    }
                }
                .opacity(0)
                .disabled(true)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(.ultraThinMaterial)
            
            // Detail content
            ParkingDetailView(parkingLocation: parkingLocation)
        }
        .background(
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
            .fill(.ultraThinMaterial)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .ignoresSafeArea(.all, edges: .bottom)
        )
    }
}

// MARK: - Preview
struct ParkingDetailBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        ParkingDetailBottomSheet(
            parkingLocation: ParkingLocation(
                id: 1,
                name: "Central Parking",
                address: "123 Collins Street, Melbourne VIC 3000",
                price: "$15",
                spots: "25",
                latitude: -37.8136,
                longitude: 144.9631
            ),
            onBack: {}
        )
    }
}
